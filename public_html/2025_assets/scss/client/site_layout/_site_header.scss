#site_header {
    background-color: $white;
    box-shadow: none;
    @include transition($transition-base);

    .skip {
        position: absolute;
        top: -1000px;
        left: -1000px;
        height: auto;
        width: auto;
        overflow: hidden;

        &:active,
        &:focus,
        &:hover {
            left: 1rem;
            top: 1rem;
            overflow: visible;
            display: block;
        }
    }

    .navbar {

        // MOBILE
        @include media-breakpoint-down(xl) {
            flex-wrap: nowrap;
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            justify-content: space-between;
            padding: 2rem 0;
        }
    }

    .navbar-brand {
        width: auto;
        flex-shrink: 0;
        flex-grow: 0;

        img {
            height: 100%;
            max-height: 100%;
            width: auto;
        }

        // MOBILE
        @include media-breakpoint-down(xl) {
            height: 60px;
        }
        @include media-breakpoint-down(lg) {
            height: 50px;
        }
        @include media-breakpoint-down(md) {
            height: 40px;
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            height: 70px;
        }
    }

    .navbar-toggler {
        --#{$prefix}navbar-toggler-padding-y: 6px;
        --#{$prefix}navbar-toggler-padding-x: #{$btn-padding-x-sm};
        --#{$prefix}navbar-toggler-font-size: #{$navbar-toggler-font-size};
        // --#{$prefix}navbar-toggler-icon-bg: #{escape-svg($navbar-light-toggler-icon-bg)};
        --#{$prefix}navbar-toggler-border-color: transparent; // #{$navbar-light-toggler-border-color};
        --#{$prefix}navbar-toggler-border-radius: .375rem;
        --#{$prefix}navbar-toggler-focus-width: 1px;
        --#{$prefix}navbar-toggler-transition: #{$navbar-toggler-transition};
        color: $black;

        &:hover {
            text-decoration: none;
            background-color: rgba($primary, .2);
        }

        &:focus {
            text-decoration: none;
            outline: 0;
            background-color: rgba($primary, .2);
            box-shadow: 0 0 3px var(--#{$prefix}navbar-toggler-focus-width);
        }
    }

    .btn-access {
        white-space: nowrap;
        
        // MOBILE
        @include media-breakpoint-down(xl) {
            margin: 0 1.5rem 3rem 1.5rem;
        }
        
        // DESKTOP
        @include media-breakpoint-up(xl) {
            
        }
    }

    #nav_primary {
        font-family: $headings-font-family;
        font-weight: $font-weight-semibold;

        .nav-item {

        }

        .nav-link {
            color: $body-color;
            white-space: nowrap;
        }

        .nav-item.active > .nav-link {
            color: $primary;
        }

        .dropdown-toggle:after {
            display: none;
        }

        .dropdown-menu {
            margin: 0;

            .nav-link {

                &:focus,
                &:hover {
                    color: $body-color;
                    background-color: rgba($secondary-100, .35);
                }
            }
        }

        // MOBILE
        @include media-breakpoint-down(xl) {
            width: calc(100vw - 1rem);
            max-width: 460px;
            margin: .375rem;
            border-radius: .25rem;

            .offcanvas-header {
                padding: .75rem .5rem .75rem 1.5rem;

                .navbar-brand {
                    order: 1;
                    padding: 0;
                }

                .btn-close {
                    order: 2;
                    margin: 0;
                    padding: 7px 16px;
                    color: $black;
                    font-size: 1.5rem;
                    line-height: 1;
                    background-image: none;
                    opacity: 1;

                    &:hover {
                        text-decoration: none;
                        background-color: rgba($primary, .2);
                        opacity: 1;
                    }

                    &:focus {
                        text-decoration: none;
                        outline: 0;
                        background-color: rgba($primary, .2);
                        box-shadow: 0 0 3px 1px;
                        opacity: 1;
                    }
                }
            }

            .offcanvas-body {
                padding: 3rem 0 1rem 0;
            }

            .btn-login {
                margin: 0 1.5rem 2rem 1.5rem;
            }

            .nav-item {

            }

            .nav-link {
                padding: .5rem 1.5rem;

                &:focus,
                &:hover {
                    color: $body-color;
                    background-color: rgba($secondary-100, .35);
                }
            }

            .dropdown-menu {
                padding: 0;
                width: 100%;
                border-color: transparent;
                border-radius: 0;
                box-shadow: none;

                .nav-item {
                    padding: 0;
                }

                .nav-link {
                    padding: .375rem 3rem;
                }
            }
        }

        // DESKTOP
        @include media-breakpoint-up(xl) {
            margin: 0 3rem;

            .nav-item {
                padding: 0 1rem;
            }

            .nav-link {
                padding: .5rem 0;

                &:focus,
                &:hover {
                    color: $primary;
                }
            }

            .dropdown-menu {
                left: 1.5rem;
                min-width: 160px;
                border-color: transparent;
                border-radius: 1rem;
                box-shadow: $box-shadow-sm;

                .nav-item {
                    padding: 0;
                }

                .nav-link {
                    padding: .375rem 1rem;
                }
            }
        }
    }

    &.header-sticky {
        box-shadow: $box-shadow-sm;
    }
}


