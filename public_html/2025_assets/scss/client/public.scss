// LOADING UP ALL THE FILES...
// This is all of the base bootstrap, set up with custom global
// variables for the client styles.

// BOOTSTRAP
// First load up BS's banner mixin
// This inserts the BS version being used on generated files
@import "../bootstrap/mixins/banner";
@include bsBanner("");

// BOOTSTRAP
// Load up BS's functions before custom, or some of our variables get grumpy.
@import "../bootstrap/functions";

// CUSTOM
// Fonts
// @import "fonts";         // CUSTOM fonts
//@import url('https://use.typekit.net/wye6ixf.css');

// CUSTOM
// Must load up our custom overriding variables before BS's.
@import "variables";

// REMOVE FROM MAPS
$colors: map-remove($colors, "indigo", "purple", "pink", "yellow", "teal", "cyan");
$theme-colors: map-remove($theme-colors, "indigo", "purple", "pink", "yellow", "teal", "cyan");

// BOOTSTRAP
// Configuration
@import "../bootstrap/variables";
@import "../bootstrap/variables-dark";
@import "../bootstrap/maps";
@import "../bootstrap/mixins";
@import "../bootstrap/utilities";

// CUSTOM OVERRIDES
// Configuration
// @import "variables-dark";
// @import "maps";
// @import "mixins";
@import "utilities";

// BOOTSTRAP
// Layout & components
@import "../bootstrap/root";
@import "../bootstrap/reboot";
@import "../bootstrap/type";
@import "../bootstrap/images";
@import "../bootstrap/containers";
@import "../bootstrap/grid";
@import "../bootstrap/tables";
@import "../bootstrap/forms";
@import "../bootstrap/buttons";
@import "../bootstrap/transitions";
@import "../bootstrap/dropdown";
@import "../bootstrap/button-group";
@import "../bootstrap/nav";
@import "../bootstrap/navbar";
@import "../bootstrap/card";
@import "../bootstrap/accordion";
@import "../bootstrap/breadcrumb";
@import "../bootstrap/pagination";
@import "../bootstrap/badge";
@import "../bootstrap/alert";
@import "../bootstrap/progress";
@import "../bootstrap/list-group";
@import "../bootstrap/close";
@import "../bootstrap/toasts";
@import "../bootstrap/modal";
@import "../bootstrap/tooltip";
@import "../bootstrap/popover";
@import "../bootstrap/carousel";
@import "../bootstrap/spinners";
@import "../bootstrap/offcanvas";
@import "../bootstrap/placeholders";

// BOOTSTRAP
// Helpers
@import "../bootstrap/helpers";

// BOOTSTRAP
// Utilities
@import "../bootstrap/utilities/api";
// scss-docs-end import-stack

// CUSTOM OVERRIDES
// Layout & components
@import "root";
@import "reboot";
@import "type";
//@import "images";
//@import "containers";
//@import "grid";
//@import "tables";
@import "forms";
@import "buttons";
//@import "transitions";
//@import "dropdown";
//@import "button-group";
//@import "nav";
//@import "navbar";
//@import "card";
//@import "accordion";
//@import "breadcrumb";
//@import "pagination";
//@import "badge";
//@import "alert";
//@import "progress";
//@import "list-group";
//@import "close";
//@import "toasts";
//@import "modal";
//@import "tooltip";
//@import "popover";
//@import "carousel";
//@import "spinners";
//@import "offcanvas";
//@import "placeholders";



// CUSTOM LAYOUT
@import "site_layout/site_header";
@import "site_layout/site_main";
@import "site_layout/site_footer";
@import "sections/sections";
@import "sections/home_hero";
@import "sections/home_2_col";
@import "sections/home_trainers";
@import "sections/home_partners";
@import "sections/home_members";
@import "sections/home_testimonials";
@import "sections/blog";
@import "sections/lookup_tool";

